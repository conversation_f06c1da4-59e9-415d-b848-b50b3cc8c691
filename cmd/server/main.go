package main

import (
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/99designs/gqlgen/graphql/handler"
	"github.com/99designs/gqlgen/graphql/playground"
	_ "github.com/lib/pq"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-cdn-service/graph"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-cdn-service/internal/config"
	repository "gitlab.ggwp.life/xbit/xbit-dex/xbit-cdn-service/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-cdn-service/internal/service"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-cdn-service/pkg/auth"
)

func main() {
	// Load configuration
	config.InitConfig()
	cfg := config.GlobalConfig

	// Initialize database connection
	db, err := initDatabase(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	// Initialize services
	r2Service, err := service.NewR2Service(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize R2 service: %v", err)
	}

	// Initialize CDN service (optional)
	cdnService, err := service.NewCDNService(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize CDN service: %v", err)
	}

	// Initialize repositories
	fileRepo := repository.NewFileRepository(db)

	// Initialize services
	fileService := service.NewFileService(fileRepo, r2Service, cdnService, cfg)

	// Initialize JWT service
	expiresTime, _ := time.ParseDuration(cfg.JWT.ExpiresTime)
	jwtService := auth.NewJWTService(cfg.JWT.SigningKey, expiresTime)

	// Initialize GraphQL resolver and handler
	resolver := graph.NewResolver(fileService)
	graphqlHandler := handler.NewDefaultServer(graph.NewExecutableSchema(graph.Config{Resolvers: resolver}))

	// Create HTTP server
	srv := createServer(graphqlHandler, jwtService, cfg)

	// Start server
	log.Printf("Database connection established to %s:%s/%s", cfg.Database.Path, cfg.Database.Port, cfg.Database.DbName)
	log.Printf("Server starting on port %s", cfg.System.Addr)

	// Print welcome message similar to xbit-agent
	printWelcomeMessage(cfg)

	if err := srv.ListenAndServe(); err != nil {
		log.Fatalf("Server failed to start: %v", err)
	}
}

func initDatabase(cfg *config.Config) (*sql.DB, error) {
	// Build connection string using config
	connStr := cfg.Database.GetDSN()

	// Open database connection
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// Configure connection pool
	db.SetMaxOpenConns(cfg.Database.GetMaxOpenConns())
	db.SetMaxIdleConns(cfg.Database.GetMaxIdleConns())

	// Test connection
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	log.Printf("Database connection established to %s:%d/%s",
		cfg.Database.Path, cfg.Database.GetPort(), cfg.Database.DbName)
	return db, nil
}

func createServer(graphqlHandler *handler.Server, jwtService *auth.JWTService, cfg *config.Config) *http.Server {
	mux := http.NewServeMux()

	// Initialize auth middleware and handlers
	authMiddleware := auth.NewAuthMiddleware(jwtService, false)
	authHandler := auth.NewAuthHandler(jwtService)
	graphqlAuthMiddleware := auth.NewGraphQLAuthMiddleware(jwtService)

	// Authentication endpoints
	mux.HandleFunc("/auth/login", authHandler.Login)
	mux.HandleFunc("/auth/refresh", authHandler.Refresh)
	mux.Handle("/auth/me", authMiddleware.RequireAuth(http.HandlerFunc(authHandler.Me)))

	// API endpoints with xbit-agent style routing
	// Health check endpoints
	mux.HandleFunc("/api/cdn-service/graphql/ping", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		fmt.Fprintf(w, `{"status": "healthy", "service": "xbit-cdn-service", "timestamp": "%s"}`,
			fmt.Sprintf("%s", cfg.System.Addr))
	})

	mux.HandleFunc("/api/cdn-service/graphql/healthz", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		fmt.Fprintf(w, `{"status": "healthy", "service": "xbit-cdn-service"}`)
	})

	// GraphQL endpoint with authentication context
	mux.Handle("/api/cdn-service/graphql", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Extract user context for GraphQL
		ctx := graphqlAuthMiddleware.ExtractUserFromRequest(r)
		r = r.WithContext(ctx)

		// Use the gqlgen handler which supports introspection
		graphqlHandler.ServeHTTP(w, r)
	}))

	// GraphQL playground
	mux.Handle("/api/cdn-service/graphql/playground", playground.Handler("GraphQL playground", "/api/cdn-service/graphql"))

	// Legacy health check endpoint (no auth required) - for backward compatibility
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		fmt.Fprintf(w, `{"status": "healthy", "service": "xbit-cdn-service"}`)
	})

	// Legacy GraphQL endpoints for backward compatibility
	mux.HandleFunc("/graphql", func(w http.ResponseWriter, r *http.Request) {
		// Redirect to new API endpoint
		http.Redirect(w, r, "/api/cdn-service/graphql", http.StatusMovedPermanently)
	})

	mux.Handle("/playground", playground.Handler("GraphQL playground", "/api/cdn-service/graphql"))

	// File upload endpoint (requires authentication) - temporarily commented out
	// mux.Handle("/api/cdn-service/upload", authMiddleware.RequirePermission(auth.PermissionWriteFiles)(
	// 	http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
	// 		if r.Method != "POST" {
	// 			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	// 			return
	// 		}

	// 		// TODO: Implement file upload handling
	// 		w.Header().Set("Content-Type", "application/json")
	// 		fmt.Fprintf(w, `{"message": "Upload endpoint - TODO: implement"}`)
	// 	}),
	// ))

	// Legacy upload endpoint for backward compatibility - temporarily commented out
	// mux.Handle("/upload", authMiddleware.RequirePermission(auth.PermissionWriteFiles)(
	// 	http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
	// 		// Redirect to new API endpoint
	// 		http.Redirect(w, r, "/api/cdn-service/upload", http.StatusMovedPermanently)
	// 	}),
	// ))

	// Apply CORS middleware to all routes - temporarily commented out
	// handler := auth.CORSMiddleware(mux)
	handler := corsMiddleware(mux)

	return &http.Server{
		Addr:    ":" + cfg.System.Addr,
		Handler: handler,
	}
}

// CORS middleware
func corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}

// JWT middleware (placeholder)
func jwtMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// TODO: Implement JWT token validation
		// For now, just pass through
		next.ServeHTTP(w, r)
	})
}

// printWelcomeMessage prints a welcome message similar to xbit-agent
func printWelcomeMessage(cfg *config.Config) {
	host := "127.0.0.1"

	fmt.Printf("\n        Welcome to XBIT CDN Service\n")
	fmt.Printf("        Current Version: v1.0.0\n")
	fmt.Printf("        Environment: %s\n", cfg.System.Env)
	fmt.Printf("        Database: %s@%s:%d/%s\n", cfg.Database.Username, cfg.Database.Path, cfg.Database.GetPort(), cfg.Database.DbName)
	fmt.Printf("        Server: %s:%s\n", host, cfg.System.Addr)
	fmt.Printf("        GraphQL Playground: http://%s:%s/api/cdn-service/graphql/playground\n", host, cfg.System.Addr)
	fmt.Printf("        GraphQL Endpoint: http://%s:%s/api/cdn-service/graphql\n", host, cfg.System.Addr)
	fmt.Printf("        Upload Endpoint: http://%s:%s/api/cdn-service/upload\n", host, cfg.System.Addr)
	fmt.Printf("        Health Check: http://%s:%s/api/cdn-service/graphql/healthz\n", host, cfg.System.Addr)
	fmt.Printf("        Legacy Endpoints: http://%s:%s/playground, http://%s:%s/graphql, http://%s:%s/health\n", host, cfg.System.Addr, host, cfg.System.Addr, host, cfg.System.Addr)
	fmt.Println()
}
